{"name": "project-name", "version": "1.0.0", "main": "index.js", "scripts": {"preinstall": "node checkNodeVersion", "prestart": "node checkNodeVersion", "start": "ts-node-dev --respawn --transpile-only --exit-child --clear ./src/index.ts", "build": "tsc --sourcemap", "prod": "node ./dist/index.js", "format": "pretty-quick --staged", "prepare": "husky", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "Encorp.io", "license": "ISC", "description": "", "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.10", "@types/winston-loggly-bulk": "^3.0.6", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^9.22.0", "eslint-config-google": "^0.14.0", "eslint-plugin-jsdoc": "^50.6.6", "husky": "^9.1.7", "lint-staged": "^15.5.0", "nodemon": "^3.1.9", "pretty-quick": "^4.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.0.0", "http": "^0.0.1-security", "mongoose": "^8.12.1", "winston": "^3.17.0", "winston-loggly-bulk": "^3.3.2"}}