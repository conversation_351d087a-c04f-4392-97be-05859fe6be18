ENV=<DEV/STAGING/PROD>
PORT=<desired_port>

# Logger
LOG_LEVEL=<If we want some specific level>
LOGGER_UPLOAD_LOGS=<true or false>
LOGGER_SUBDOMAIN=<Subdomain for the logger>
LOGGER_TOKEN=<Token for the logger>
LOGGER_TAGS=<Tags for the logger separated by comma (,)>

# Mongoose
MONGODB_HOST=<your_mongodb_host>
MONGODB_PORT=<port_that_mongodb_runs>
MONGODB_DATABASE_NAME=<mongodb_database_name>
MONGODB_USERNAME=<mongodb_username>
MONGODB_PASSWORD=<mongodb_user_password>
MONGODB_URI=<mongodb_uri>
CA_CERT=<ca_cert>

# JWT
JWT_PUBLIC_KEY=<your_public_key>
JWT_SECRET_KEY=<your_private_key>
JWT_ALGORITHM="ES512"
TEST_ACCESS_TOKEN=<your_test_jwt_token> // this is only for dev environment, it is not required in production

# OpenAI
OPENAI_API_KEY=<your_open_ai_key>
OPENAI_API_ORGANIZATION=<your_open_ai_organization>
OPENAI_API_PROJECT=<your_open_ai_project>

# Kafka
KAFKA_BROKER_URL=<your_kafka_broker_url>
KAFKA_GROUP_ID=<your_kafka_group_id>

#Redis 
REDIS_URL=<your_redis_url>
REDIS_PORT=<your_redis_port>
REDIS_PASSWORD=<your_redis_password>

# Middlewares
IP_WHITELIST=<comma-separated IP list>
