import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';

declare global {
  namespace Express {
    interface Request {
      user?: {
        username: string;
        userId: string;
        role: string;
        roleIsActive: boolean;
        permissions: {
          action: string;
          object: string;
          description?: string;
          isActive: boolean;
        }[];
      };
    }
  }
}

export interface CustomRequestWithUser
  extends Request<
    ParamsDictionary, // params
    any, // response body
    any, // request body
    ParsedQs, // query strings
    Record<string, any> // locals
  > {
  user?: any | RyvylUserAgent;
}
