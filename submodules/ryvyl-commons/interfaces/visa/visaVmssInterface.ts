import { RyvylUserAgent } from "../express/ryvylUserAgent";

export interface VisaMatchData {
  _id?: string;
  requestMadeToCheckMerchant?: number; // How much request made to check merchant
  data?: VisaMatchDataItem[];
  errorMessageRequest?: any;
  failedRequest?: number;
  isThereAnyMatch?: boolean;
  createdAt?: Date;
  resolvedVisaMatch?: boolean;
  userAgent?: RyvylUserAgent;
}

export interface VisaMatchDataItem {
  _id?: string;
  url?: string;
  isMatch?: boolean;
  resolvedVisaMatch?: boolean;
  visaTotalCountMatches?: number; // Count of the matches of the request
  visaResultData?: any; // Fear we are saving all the data from the response from visa
  errorMessage?: any;
  createdAt?: Date;
  userAgent?: RyvylUserAgent;
}
