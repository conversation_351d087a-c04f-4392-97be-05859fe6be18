import { SumsubErrorResponse } from './webSDKInterface';

export interface ExtendedApplicantRequest extends CreateApplicantRequest {
  levelName: string;
}

export interface CreateApplicantRequest {
  externalUserId: string; // required
  info?: {
    companyInfo?: CompanyInfo;
  };
  sourceKey?: string;
  email?: string;
  phone?: string;
  lang?: string;
  metadata?: { key: string; value: string }[];
  fixedInfo?: FixedInfo;
  type?: 'company' | 'individual';
}

export interface FixedInfo {
  companyInfo?: CompanyInfo;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  legalName?: string;
  gender?: 'M' | 'F';
  dob?: string; // YYYY-MM-DD
  placeOfBirth?: string;
  countryOfBirth?: string; // Alpha-3 code and need to be in Uppercase
  stateOfBirth?: string;
  country?: string; // Alpha-3 code and need to be in Uppercase
  nationality?: string; // Alpha-3 code and need to be in Uppercase
  addresses?: Address[];
  tin?: string;
  taxResidenceCountry?: string; // Alpha-3 code and need to be in Uppercase
  type?: string;
}

export interface CompanyInfo {
  companyName?: string;
  registrationNumber?: string;
  country?: string; // required, Alpha-3 code and need to be in Uppercase
  alternativeNames?: string[];
  legalAddress?: string;
  address?: CompanyAddress;
  incorporatedOn?: string; // YYYY-MM-DD
  type?: string;
  email?: string;
  phone?: string;
  controlScheme?: string;
  taxId?: string;
  registrationLocation?: string;
  website?: string;
  postalAddress?: string;
  noUBOs?: boolean;
  noShareholders?: boolean;
}

export interface CompanyAddress {
  street?: string;
  subStreet?: string;
  town?: string;
  state?: string;
  postCode?: string;
  country?: string; // Alpha-3 code and need to be in Uppercase
}

export interface Address {
  country?: string; // Alpha-3 code and need to be in Uppercase
  postCode?: string;
  town?: string;
  street?: string;
  subStreet?: string;
  state?: string;
  buildingName?: string;
  flatNumber?: string;
  buildingNumber?: string;
  formattedAddress?: string;
}

export interface Beneficiary {
  applicantId?: string; // Required in KYB 1.0 only
  id?: string; // Used in KYB 2.0 only
  positions?: string[]; // Used in KYB 1.0 only, deprecated in KYB 2.0
  type?: string; // Used in KYB 1.0, deprecated in KYB 2.0
  types?: ('ubo' | 'shareholder' | 'representative' | 'director')[]; // Used in KYB 2.0 only
  shareSize?: number; // % of ownership
  inRegistry?: boolean | null; // Used in KYB 1.0 only, deprecated in KYB 2.0
  imageIds?: string[]; // Image IDs (first = front, rest = back sides)
}

export interface ChangeCompanyInfo extends CompanyInfo {
  companyName: string;
  country: string; // Alpha-3 code and need to be in Uppercase
}

export interface LinkBeneficiaryToCompany {
  companyApplicantId: string; // Sumsub company applicantId
  beneficiaryData: {
    applicantId?: string; // Sumsub individualapplicantId
    types: ('ubo' | 'shareholder' | 'representative' | 'director')[];
    shareSize?: string;
    beneficiaryInfo?: LinkBeneficiaryInfoData;
  };
  metadata?: MetadataItem[];
}

export interface LinkBeneficiaryInfoData {
  // Personal info
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  dob?: string; // Format: YYYY-MM-DD
  countryOfBirth?: string; // Alpha-3 code
  stateOfBirth?: string;
  placeOfBirth?: string;
  legalName?: string; // Not validated, used for navigation
  gender?: 'M' | 'F';
  nationality?: string; // Alpha-3 code
  country?: string; // Alpha-3 code
  tin?: string;
  taxResidenceCountry?: string; // Alpha-3 code

  // Company-related info (for company applicants only)
  companyName?: string;
  registrationNumber?: string;
  registrationLocation?: string;
  type?: string;
  incorporatedOn?: string; // Format: YYYY-MM-DD
  legalAddress?: string;
  postalAddress?: string;
  controlScheme?: string;
  taxId?: string;
  website?: string;

  // Address details
  street?: string;
  subStreet?: string;
  buildingNumber?: string;
  flatNumber?: string;
  town?: string;
  state?: string;
  postCode?: string;

  // System-level info
  externalUserId?: string;
}

export interface MetadataItem {
  key: string;
  value: string;
}

export interface LinkBeneficiaryResponse extends SumsubErrorResponse {
  id: string;
  applicantId?: string;
  applicant?: any; // this is applicant object
  shareSize?: number;
  types: Array<'ubo' | 'shareholder' | 'representative' | 'director'>;
  beneficiaryInfo?: LinkBeneficiaryInfoData;
  metadata?: Array<MetadataItem>;
}
