import { ChangeRecord } from '../../utils/deepTransformationOnData';
import { RyvylUserAgent } from '../express/ryvylUserAgent';

export interface MastercardMatchData {
  _id?: string;
  requestMadeToCheckMerchant?: number; // How much request made to check merchant
  data?: MastercardMatchDataItem[];
  errorMessageRequest?: any;
  failedRequest?: number;
  isThereAnyMatch?: boolean;
  resolvedMastercardMatch?: boolean;
  userAgent?: RyvylUserAgent;
}

export interface MastercardMatchDataItem {
  _id?: string;
  url?: string;
  isMatch?: boolean;
  resolvedMastercardMatch?: boolean;
  mastercardTotalCountMatches?: number; // Count of the matches of the request
  mastercardRefNumberMatched?: string;
  errorMessage?: any;
  userAgent?: RyvylUserAgent;
  changesTransliterate?: ChangeRecord[];
  changesPatternValidation?: ChangeRecord[];
}
