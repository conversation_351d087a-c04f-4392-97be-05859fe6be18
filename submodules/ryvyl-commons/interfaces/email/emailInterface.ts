import { ObjectId } from "mongoose";

export enum EmailStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending'
}

export interface EmailLog {
  referenceId: string | ObjectId;
  referenceModel: string;
  urlToSendEmail?: string;
  to: string[]; // Support for multiple recipients
  cc?: string[];
  bcc?: string[];
  subject?: string;
  body?: any;
  status: EmailStatus;
  errorMessage?: any;
  emailType?: string; // Example: 'welcome' | 'onboarding' | 'alert' | 'notification' | 'custom';
  retries: number; // Number of retry attempts if any
  metadata?: any;
}
