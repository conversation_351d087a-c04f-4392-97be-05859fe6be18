# Ryvyl Commons Submodule

The Ryvyl Commons Submodule provides a set of reusable services and utilities for building applications within the Ryvyl ecosystem. It includes functionalities for authentication, logging, and database connectivity, ensuring a consistent and efficient development experience.

## Features

- **Authentication:** Secure token generation and verification using JWT.
- **Logging**: Centralized logging with Winston and Loggly integration for monitoring and debugging.
- **Database Connectivity**: Simplified MongoDB connection management with Mongoose.
- **Validation**: Input validation utilities to ensure data integrity.

## Installation

To get started with the Ryvyl Commons Submodule, clone the repository and install the dependencies:

1. **Clone the repository:**

   ```bash
   git clone https://github.com/encorp-io/ryvyl-commons.git
   cd ryvyl-commons
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

## Environment Variables

To continue with local development and running tests, you'll need to set up the environment variables for the JWT. Additionally, any service that uses the Ryvyl Commons Submodule must have most of the environment variables configured to function correctly. Here are all the variables:

```
ENV=<DEV/STAGING/PROD>
PORT=<desired_port>

# Logger - these are optional, but good to fill
LOG_LEVEL=<If we want some specific level>
LOGGER_UPLOAD_LOGS=<true or false>
LOGGER_SUBDOMAIN=<Subdomain for the logger>
LOGGER_TOKEN=<Token for the logger>
LOGGER_TAGS=<Tags for the logger separated by comma (,)>

# Mongoose
MONGODB_HOST=<your_mongodb_host>
MONGODB_PORT=<port_that_mongodb_runs>
MONGODB_DATABASE_NAME=<mongodb_database_name>
MONGODB_USERNAME=<mongodb_username>
MONGODB_PASSWORD=<mongodb_user_password>
MONGODB_URI=<mongodb_uri>
CA_CERT=<ca_cert>

# For the authentication middle from the submodules
JWT_PUBLIC_KEY=<your_public_key>
JWT_SECRET_KEY=<your_private_key>
JWT_ALGORITHM="ES512"
TEST_ACCESS_TOKEN=<your_test_jwt_token> // this is only for dev environment, it is not required in production

# OpenAI
OPENAI_API_KEY=<your_open_ai_key>
OPENAI_API_ORGANIZATION=<your_open_ai_organization>
OPENAI_API_PROJECT=<your_open_ai_project>

# Kafka
KAFKA_BROKER_URL=<your_kafka_broker_url>
KAFKA_GROUP_ID=<your_kafka_group_id>

#Redis
REDIS_URL=<your_redis_url>
REDIS_PORT=<your_redis_port>
REDIS_PASSWORD=<your_redis_password>

# Middlewares
IP_WHITELIST=<IP comma-separated list>
```

## Local Development

Before committing any changes, it is good to execute the prettifier, however, we have enabled husky and it automatically formats the code on a pre-commit.

#### Running Tests

Additionally, it is good to run the tests to ensure that nothing breaks and you can do it by executing the following command:

```bash
npm run test
```

**Note:** You must configure some environment variables and you can check the details in the [environment variables section](#environment-variables) in order to execute the tests.

## Adding this Repository as a Submodule

If you want to use this repository as a submodule in your project, follow these steps.

#### **1. Adding the Submodule**

Run the following command in your project’s root directory:

```bash
git submodule add https://github.com/encorp-io/ryvyl-commons.git submodules/ryvyl-commons
```

This will:

- Clone the repository inside the submodules/ryvyl-commons directory.
- Create a `.gitmodules` file to store submodule configuration

**Note:** Check the [checklist](#checklist-for-integrating-the-submodule) to ensure proper integration.

#### **2. Initializing and Updating the submodule**

After adding the submodule, initialize and fetch its content with:

```bash
git submodule update --init --recursive
```

**Note:** This might not be required initially, however, it ensures the submodule is correctly set up and ready to use. Additionally, if it has nested submodule(s), it will fetch them, too.

#### **3. Tracking a Specific Branch**

By default, submodules point to a specific commit (usually the main/master branch) instead of automatically tracking a branch.
To change this behavior and always pull the latest changes from a branch, update your .gitmodules file:

```
[submodule "submodules/ryvyl-commons"]
   path = submodules/ryvyl-commons
   url = https://github.com/encorp-io/ryvyl-commons.git
   branch = new-feature-branch
```

Then, apply the changes by running:

```bash
git submodule update --init --recursive --remote submodules/ryvyl-commons
```

#### **4. Pulling Updates from the Submodule**

If changes are made to the submodule repository, update it in your project with:

```bash
git submodule update --remote --merge
```

Now, you have full access to the features of ryvyl-commons inside your project! 🚀

## Checklist for integrating the submodule

This is a helpful checklist that you can consider when integrating the submodule.

- Use the git submodule [add](#1-adding-the-submodule) command initially
- Configure submodule's dependencies to be installed next with the main ones by adding the following line in the `package.json`:
  ```
  "dependencies": {
     "common": "file:submodules/ryvyl-commons",
     ...
  }
  ```
- Additionally, add the update submodules script in the `package.json`. For example, it can be:
  ```
  "update-ryvyl-commons": "git submodule update --init --recursive --remote submodules/ryvyl-commons"
  ```
- Leave the `rootDir` in the `tsconfig.json` to be `./`, or remove it at all, and include the types in the array of includes
  ```
  "rootDir": "./",
  ...
  "include": ["src/**/*", "submodules/ryvyl-commons/types/**/*"],
  ```
- If any command relies on `dist/index.js`, update it to `dist/src/index.js`
- If you have a jest config, then add the submodules to be ignored
  ```
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/submodules/', '<rootDir>/dist/']
  ```
- Add the submodules directory in the `.prettierignore`

## Easy access to your module:

If you want to access your module easily, without having to type the paths back "../../../submodules/myModule" <br>
You can easily access it with "@submodules/myModule...", here's how:

1. Install the following package:

```
npm install module-alias
```

2. Configure your package.json file, by adding the following:

```
  "_moduleAliases": {
    "@submodules": "./submodules"
  }
```

3. Register Aliases in your `tsconfig.json` or `jsconfig.json` file:

```
"compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@submodules/*": ["submodules/*"]
    }
  }
```

4. Register module-alias in the top level file - `index.ts` for example:

```
import 'module-alias/register';
```

Now you should be able to easily access your submodules, here's an example for the logger:

```
import logger from '@submodules/ryvyl-commons/services/loggerService';
```

## Docker image configuration:

To run properly the submodule for docker image you will need to update the `yaml` configuration:

```
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          submodules: true # Fetch submodules
          fetch-depth: 0 # Ensure full commit history
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }} # Set the access token
```

Set up your personal token by getting it from:
1 - github -> settings -> Developer settings -> Personal access token -> Tokens(classic) -> Generate new access token (classic) -> Select Repo and workflow settings -> Generate token<br>
Copy the token and set it to PERSONAL_ACCESS_TOKEN secrets in repository!

It is also recommended to add the following job:

```
      - name: Update submodules
        run: git submodule update --init --recursive --remote submodules/ryvyl-commons

```

### ⚠️ NOTICE: Handling module-alias in Docker Builds

If you are using **module-alias** or TypeScript **path aliases** (via tsconfig.json), these paths will not be recognized in Docker **unless** explicitly resolved. This happens because Docker typically executes only the transpiled JavaScript (dist/), where aliases are no longer valid unless handled manually or with runtime helpers.

#### Recommended Solution

Use the script located at `/scripts/replace-paths.js` to automatically convert path aliases to relative paths before building the project. Here’s how:

**1. Use the script in a separate command**

Update your package.json with a dedicated script:

```
  "replace-paths": "node submodules/ryvyl-commons/scripts/replace-paths.js ./src/",
```

**2. Run this in your Dockerfile before npm run build:**

```
  RUN npm run replace-paths && npm run build
```

**Note:** Do not run the path-replace script locally unless you intend to overwrite your imports. This can break development workflows where you rely on alias-based imports.

### ⚠️ NOTICE: Handling module-alias in Tests

If you are using **module-alias** or TypeScript **path aliases** (via tsconfig.json), these paths will not be recognized in Jest **unless** explicitly resolved.

#### Recommended Solution

Install the dev-package dependency:

```
npm install ts-jest
```

This will allow you to use `pathsToModuleNameMapper` in your jest.config.js file. <br>

Modify the jest.config.js file to include the following:

```js
const { pathsToModuleNameMapper } = require('ts-jest/utils');
const { compilerOptions } = require('./tsconfig');

module.exports = {
  ...
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>/' }),
  ...
};
```

This way, the paths will be recognized in Jest.

**Note:** Since this is dev dependency, it will not affect the production build. But it also cannot be installed in the main project from the submodule, since it is a dev dependency.
