import cron, { ScheduleOptions } from 'node-cron';

import { CronJobOptions } from '../types/cron';

export const EVERY_MINUTE = '* * * * *'; // every minute
export const EVERY_HOUR = '0 * * * *'; // every hour
export const EVERY_DAY = '0 0 * * *'; // every day at midnight
export const EVERY_WEEK = '0 0 * * 1'; // every week at midnight on Sunday to Monday (00:00:00)
export const EVERY_MONTH = '0 0 1 * *'; // every month at midnight on the first day of the month (after last day of month passed)

/**
 * Starts a cron job with the given name, logger, and job function.
 * @param {CronJobOptions} param0 - The options for the cron job as object - name, logger, job function.
 * @param {string} cronExpression - The cron expression for the cron job.
 * @param {ScheduleOptions} options - The options for the cron job, e.g., timezone, scheduled, etc.
 * @returns {Promise<void>} - A promise that resolves when the cron job is started.
 */
export async function StartCronJob({ name, logger, jobFunction }: CronJobOptions, cronExpression: string, options?: ScheduleOptions): Promise<void> {
  let isCronStarted = false;
  let isCronCycleActive = false;

  if (!isCronStarted) {
    isCronStarted = true;
    logger.info(`${name} Cron initiated.`);

    cron.schedule(cronExpression, async () => {
      if (isCronCycleActive) {
        logger.info(`${name} cron cycle is active. Skipping cycle.`);
        return;
      }

      isCronCycleActive = true;

      await jobFunction({ name, logger });

      isCronCycleActive = false;
    }, options);
  }
}
