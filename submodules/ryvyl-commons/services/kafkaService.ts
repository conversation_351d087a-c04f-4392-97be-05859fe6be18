import { Kafka, Consumer, Producer, EachMessagePayload, PartitionAssigner, PartitionAssigners } from 'kafkajs';

import { KAFKA } from '../config';
import logger from './loggerService';

interface KafkaConsumerOptions {
  consumerConfig?: KafkaConsumerConfig;
  topicConfig?: { numPartitions?: number; replicationFactor?: number };
}
export interface KafkaConsumerConfig {
  partitionAssigners?: PartitionAssigner[];
  sessionTimeout?: number; // ms
  rebalanceTimeout?: number; // ms
  heartbeatInterval?: number; // ms
  metadataMaxAge?: number; // ms
  allowAutoTopicCreation?: boolean;
  maxBytesPerPartition?: number; // bytes
  minBytes?: number; // bytes
  maxBytes?: number; // bytes
  maxWaitTimeInMs?: number; // ms
  retry?: {
    retries?: number;
    initialRetryTime?: number;
    maxRetryTime?: number;
    factor?: number;
    multiplier?: number;
  };
  readUncommitted?: boolean;
  maxInFlightRequests?: number;
  rackId?: string;
}

export class KafkaService {
  private kafka: Kafka;
  private producer: Producer;
  private consumers: Map<string, Consumer> = new Map();
  private isConnected: boolean = false;

  constructor() {
    this.kafka = new Kafka({
      brokers: KAFKA.BROKERS
    });

    this.producer = this.kafka.producer({
      allowAutoTopicCreation: true
    });
  }

  // 🔹 Connect Kafka Producer
  async connect(): Promise<void> {
    if (this.isConnected) return;

    try {
      await this.producer.connect();
      this.isConnected = true;
      logger.info('✅ Connected to Kafka Producer');
    } catch (error: any) {
      logger.error(`❌ Error connecting to Kafka Producer: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Disconnect Kafka
  async disconnect(): Promise<void> {
    if (!this.isConnected) return;

    try {
      // Disconnect all consumers
      for (const [name, consumer] of this.consumers) {
        await consumer.disconnect();
        logger.info(`✅ Disconnected consumer: ${name}`);
      }

      await this.producer.disconnect();
      this.isConnected = false;
      logger.info('✅ Disconnected from Kafka');
    } catch (error: any) {
      logger.error(`❌ Error disconnecting from Kafka: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Ensure the Topic Exists (and Create if not)
  private async ensureTopicExists(
    topic: string,
    numPartitions: number,
    replicationFactor: number,
    retry: number = 0
  ): Promise<void> {
    const admin = this.kafka.admin();

    await admin.connect();
    const topics = await admin.listTopics();

    let currentPartitions = 0;
    if (topics.includes(topic)) {
      const topicMetadata = await admin.fetchTopicMetadata({ topics: [topic] });
      currentPartitions = topicMetadata.topics[0].partitions.length;
      logger.info(`Topic "${topic}" currently has ${currentPartitions} partitions`);
    }

    const desiredPartitions = numPartitions;

    if (!topics.includes(topic) || currentPartitions !== desiredPartitions) {
      // If the topic already exists we need to delete it to recreate it with more or less partitions
      if (topics.includes(topic)) {
        logger.info(
          `Topic "${topic}" exists but needs more partitions. Current: ${currentPartitions}, Desired: ${desiredPartitions}`
        );
        // Delete the topic to recreate it with more partitions
        await admin.deleteTopics({ topics: [topic] });
        logger.info(`Deleted topic "${topic}" to recreate with more partitions`);
      }

      logger.info(`Creating topic "${topic}" with ${desiredPartitions} partitions`);

      try {
        await admin.createTopics({
          topics: [{ topic: topic, replicationFactor: replicationFactor }]
        });

        await admin.createPartitions({
          topicPartitions: [{ topic: topic, count: desiredPartitions }]
        });
      } catch (error: any) {
        // We have this logic because when we recreate topic with more partitions (50) to smaller partitions (5)
        // First we have this error and on a second time we create the topic with the desired partitions successfully
        if (error.type === 'UNKNOWN_TOPIC_OR_PARTITION' && retry < 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await this.ensureTopicExists(topic, numPartitions, replicationFactor, retry + 1);
          return;
        } else {
          throw error;
        }
      }

      // This is because for the first time we create the topic
      // we need to publish some messages to the topic to make it available for the consumers
      // because if we don't do this the consumer will not be able to consume the messages
      // when is off and if the publisher publish a message and after consumer go back this message will be lost
      // because for the consumer to be able to consume the messages it need to have been consumed at least 1 message
      setTimeout(async () => {
        for (let index = 0; index < desiredPartitions * 10; index++) {
          this.publish(topic, index.toString(), 'Test Topic');
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
        logger.info(`✅✅✅✅ Consumer: ${topic} is ready to be used ✅✅✅✅`);
      }, 15000);

      logger.info(`Topic "${topic}" created successfully with ${desiredPartitions} partitions`);
    }

    await admin.disconnect();
  }

  // 🔹 Subscribe to Kafka Topics with a dedicated consumer
  async subscribe(
    topics: string[],
    fromBeginning: boolean,
    callback: (topic: string, message: any) => Promise<void>,
    consumerName: string = 'default',
    options: KafkaConsumerOptions = {}
  ): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka is not connected');
    }
    const defaultOptions: KafkaConsumerOptions = {
      topicConfig: {
        numPartitions: 5, // number of partitions for the topic (how many parallel message can be processed in a same time)
        replicationFactor: 1
      },
      consumerConfig: {
        sessionTimeout: 15000, // seconds before consumer is considered dead
        heartbeatInterval: 3000,
        maxWaitTimeInMs: 1000
      }
    };

    const finalOptions: KafkaConsumerOptions = {
      topicConfig: {
        ...defaultOptions.topicConfig,
        ...options.topicConfig
      },
      consumerConfig: {
        ...defaultOptions.consumerConfig,
        ...options.consumerConfig
      }
    };

    try {
      // Create a new consumer for this subscription if it doesn't exist
      if (!this.consumers.has(consumerName)) {
        const consumer = this.kafka.consumer({
          groupId: `${KAFKA.GROUP_ID}-${consumerName}`,
          partitionAssigners: [PartitionAssigners.roundRobin],
          ...finalOptions.consumerConfig
        });
        await consumer.connect();
        this.consumers.set(consumerName, consumer);
      }

      const consumer = this.consumers.get(consumerName)!;

      for (const topic of topics) {
        let currentPartitions = finalOptions.topicConfig?.numPartitions;
        // Ensure all topics exist
        await this.ensureTopicExists(
          topic,
          finalOptions.topicConfig?.numPartitions as number,
          finalOptions.topicConfig?.replicationFactor ?? 1
        );

        await consumer.subscribe({ topics: [topic], fromBeginning });

        // Start consuming messages
        await consumer.run({
          partitionsConsumedConcurrently: currentPartitions, // How many partitions can be consumed parallel
          eachMessage: async ({ topic, partition, message, heartbeat, pause }: EachMessagePayload) => {
            try {
              if (!message.value) return;

              const messageValue = message.value.toString();
              let parsedMessage;
              try {
                parsedMessage = JSON.parse(messageValue);
              } catch (err) {
                parsedMessage = messageValue;
              }

              // This is to avoid the consumer to consume the test topic
              if (parsedMessage === 'Test Topic') return;

              await withTimeout(callback(topic, parsedMessage), finalOptions.consumerConfig?.sessionTimeout as number);

              await heartbeat();
            } catch (err: any) {
              logger.error(`❌ Error processing message from topic: "${topic}", error: ${err.message}`);
            }
          }
        });
        logger.info(`✅ Consumer ${consumerName} subscribed to Kafka topics: ${topics}`);
      }
    } catch (error: any) {
      logger.error(`❌ Error subscribing consumer ${consumerName} to Kafka topics: ${topics}, error: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Publish Messages to Kafka Topics
  async publish(topic: string, key: string, message: any): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka is not connected');
    }

    try {
      await this.producer.send({
        topic,
        messages: [{ key, value: JSON.stringify(message) }]
      });
    } catch (error: any) {
      logger.error(`❌ Error sending message to topic: "${topic}", error: ${error.message}`);
      throw error;
    }
  }
}

// This is to handle the timeout of the callback function / or when a callback function is taking to long to process the message
async function withTimeout<T>(promise: Promise<T>, ms: number): Promise<T> {
  return new Promise((resolve, reject) => {
    // We subtract 1 seconds from the timeout becouse anfre ms is timeout the consumer will be considered dead
    const timeout = setTimeout(() => reject(new Error('Callback timeout')), ms - 1000);
    promise
      .then((res) => {
        clearTimeout(timeout);
        resolve(res);
      })
      .catch((err) => {
        clearTimeout(timeout);
        reject(err);
      });
  });
}

// ✅ Export the instance to use Kafka for both publishing and subscribing
export const kafkaService = new KafkaService();
