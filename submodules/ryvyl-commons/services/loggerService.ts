import winston from 'winston';
import { Loggly } from 'winston-loggly-bulk';
import { getEnvOrRevert, IS_PRODUCTION, LOG_LEVEL } from '../config';

/**
 * @param entry The message to be logged
 * @returns The formatted message
 */
function formatConsoleLog(entry: any): string {
  const message = typeof entry.message === 'object' ? JSON.stringify(entry.message, null, 2) : entry.message;
  if (IS_PRODUCTION) {
    return `[${entry.level}] [${entry.label}] ${entry.stack ? entry.stack : message}`;
  }
  return `[${entry.level}] ${entry.timestamp} [${entry.label}] ${entry.stack ? entry.stack : message}`;
}

const getLoggerFormat = (service: string): winston.Logform.Format => {
  if (IS_PRODUCTION) {
    return winston.format.combine(
      winston.format.splat(),
      winston.format.colorize({ all: false }),
      winston.format.errors({ stack: true }),
      winston.format.label({ label: service }),
      winston.format.printf(formatConsoleLog),
      winston.format.metadata()
    );
  }

  return winston.format.combine(
    winston.format.splat(),
    winston.format.colorize({ all: true }),
    winston.format.errors({ stack: true }),
    winston.format.timestamp(),
    winston.format.label({ label: service }),
    winston.format.printf(formatConsoleLog),
    winston.format.metadata()
  );
};

export const getLoggerConfig = (service: string) => {
  return {
    format: getLoggerFormat(service),
    transports: [
      new winston.transports.Console({
        level: LOG_LEVEL
      }),
      ...(getEnvOrRevert('LOGGER_UPLOAD_LOGS') === 'true'
        ? [
          new Loggly({
            token: getEnvOrRevert('LOGGER_TOKEN'),
            subdomain: getEnvOrRevert('LOGGER_SUBDOMAIN'),
            tags: getEnvOrRevert('LOGGER_TAGS')?.split(','),
            json: true
          })
        ]
        : [])
    ],
    exitOnError: false
  };
};

export function createLoggerInstance(name: string) {
  return winston.createLogger(getLoggerConfig(name));
}

const logger = createLoggerInstance('General');
export const AIClassifierLogger = createLoggerInstance('AIClassifier');
export const IPWhitelistLogger = createLoggerInstance('IPWhitelist');

logger.info('Logger attached.');

export default logger;
