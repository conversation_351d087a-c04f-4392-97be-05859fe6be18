import countries, { Alpha2Code } from 'i18n-iso-countries';

export function getTwoLetterCodeFromThreeLetterCode(threeLetterCode: string) {
  return countries.toAlpha2(threeLetterCode);
}

export function getTwoLetterCodeFromFullName(countryName: string): string | undefined {
  // Convert the full country name to its two-letter code
  return countries.getAlpha2Code(countryName, 'en');
}

export function getThreeLetterCodeFromFullName(countryName: string): string | undefined {
  // Convert the full country name to its two-letter code
  return countries.getAlpha3Code(countryName, 'en');
}

export function getFullNameFromThreeLetterCode(threeLetterCode: string): string | undefined {
  return countries.getName(threeLetterCode, 'en');
}

export function isValidTwoLetterCountryCode(countryCode: string): boolean {
  // Check if the country code is valid by trying to get its name
  // If it returns a name, the code is valid; if it returns undefined, it's invalid
  return countries.getName(countryCode, 'en') !== undefined;
}

export function isValidThreeLetterCountryCode(countryCode: string): boolean {
  // Check if the 3-letter country code is valid
  return countries.getName(countryCode, 'en') !== undefined;
}


/**
 * Convert 2-letter country code to 3-letter country code
 * @param country2AlphaCode - 2-letter country code
 * @returns 3-letter country code
 */
export function get3LetterCountryCode(country2AlphaCode: string) {

  // Example: Convert 2-letter code to 3-letter code
  const alpha2Code = country2AlphaCode.toLocaleUpperCase() as Alpha2Code; // 2-letter code
  const alpha3Code = countries.alpha2ToAlpha3(alpha2Code);

  return alpha3Code;
}

