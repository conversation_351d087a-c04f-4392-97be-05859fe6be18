export type TransformOptions = {
  /** Types to transform (default: ['string']) */
  targetTypes?: ('string' | 'number' | 'boolean')[];
  /** Paths to skip transformation */
  skipPaths?: string[];
  /** Maximum depth to traverse */
  maxDepth?: number;
  /** Whether to track changes */
  trackChanges?: boolean;
  /** Custom condition function to determine if value should be transformed */
  shouldTransform?: (value: any, path: string) => boolean;
};

export type ChangeRecord = {
  path: string;
  originalValue: any;
  newValue: any;
  type: string;
};

/** 
 * This function is used to transform the data.
 * It is used to pass any data and transform it with any function.
 *
 * @param {Object} data - Data to transform. Can be any type of data, string, number, object, array, etc.
 * @param {Function} transformFunction - Function to transform the data.
 * @param {Object} options - Options to configure the transformation.
 * @param {Array<string>} options.targetTypes - Types to transform (default: ['string']).
 * @param {Array<string>} options.skipPaths - Paths to skip transformation.
 * @param {number} options.maxDepth - Maximum depth to traverse.
 * @param {boolean} options.trackChanges - Whether to track changes.
 * @param {Function} options.shouldTransform - Custom condition function to determine if value should be transformed.
 * @param {string} path - The path to the object.
 * @param {ChangeRecord[]} changes - To store the changes, if we want to use it later.
 * @param {WeakSet<object>} visited - To store the visited objects, to avoid infinite recursion.
 * @param {number} depth - The depth of the object, to track the depth of the object.
 * @returns {Object} The transformed object.
 */
export function deepTransformationOnData<T = any>(
  data: T,
  transformFunction: (value: any, path: string) => any,
  options: TransformOptions = {},
  path: string = '',
  changes: ChangeRecord[] = [],
  visited: WeakSet<object> = new WeakSet(),
  depth: number = 0
): T {
  const {
    targetTypes = ['string'],
    skipPaths = [],
    maxDepth = 100,
    trackChanges = true,
    shouldTransform
  } = options;

  // Prevent infinite recursion
  if (depth > maxDepth) {
    throw new Error(`Maximum depth ${maxDepth} reached at path: ${path}`);
  }

  // Skip if path is in skipPaths
  if (skipPaths.some(skipPath => path.startsWith(skipPath))) {
    return data;
  }

  // Handle circular references
  if (data !== null && typeof data === 'object' && visited.has(data as object)) {
    return data;
  }

  if (Array.isArray(data)) {
    if (data.length > 0 && typeof data[0] === 'object') {
      visited.add(data);
    }
    
    return data.map((item, index) =>
      deepTransformationOnData(
        item,
        transformFunction,
        options,
        `${path}[${index}]`,
        changes,
        visited,
        depth + 1
      )
    ) as T;
  }
  
  if (data !== null && typeof data === 'object') {
    visited.add(data as object);
    
    const newObj: any = {};
    for (const key of Object.keys(data)) {
      const newPath = path ? `${path}.${key}` : key;
      newObj[key] = deepTransformationOnData(
        (data as any)[key],
        transformFunction,
        options,
        newPath,
        changes,
        visited,
        depth + 1
      );
    }
    return newObj;
  }
  
  // Check if we should transform this value
  const dataType = typeof data;
  const shouldTransformValue = shouldTransform 
    ? shouldTransform(data, path)
    : targetTypes.includes(dataType as any);
    
  if (shouldTransformValue) {
    const transformedValue = transformFunction(data, path);
    
    if (trackChanges && transformedValue !== data) {
      changes.push({
        path,
        originalValue: data,
        newValue: transformedValue,
        type: dataType
      });
    }
    
    return transformedValue;
  }
  
  return data;
}

/**
 * This function is used to get the clear path without array index.
 * 
 *
 * @param {string} path - The path to get the clear path without array index.
 * @returns {string} The clear path without array index.
 * @example
 * getClearParhWithoutArrayIndex('merchant.name') // 'merchant.name'
 * getClearParhWithoutArrayIndex('merchant.name[0]') // 'merchant.name'
 * getClearParhWithoutArrayIndex('merchant.name[340].address[1].city[2].state[3].country[4].zip[5]') // 'merchant.name.address.city.state.country.zip'
 */
export function getClearParhWithoutArrayIndex(path: string): string {
  const patternPath = path.replace(/\[\d+\]/g, '');
  return patternPath ?? '';
}
