const activeTasks = new Set<Promise<any>>();

export function trackTask<T>(promise: Promise<T>): Promise<T> {
  activeTasks.add(promise);
  promise.finally(() => activeTasks.delete(promise));
  return promise;
}

/**
 * Wait for critical tasks to complete or timeout
 * @param timeoutMs - The timeout in milliseconds (default: 180000 = 3 minutes) for await critical tasks to complete.
 * @returns A promise that resolves when all critical tasks are completed or rejects with an error if the timeout is reached.
 */
export async function waitForCriticalTasks(timeoutMs: number = 180000): Promise<void> {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Timeout waiting for critical tasks after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  await Promise.race([
    Promise.all([...activeTasks]),
    timeoutPromise
  ]);
}
