/**
 * Recursively trims all string values in an object, including nested objects and arrays
 * @param obj - The object to trim
 * @returns A new object with all string values trimmed
 */
export function deepTrimStrings<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return obj.trim() as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepTrimStrings(item)) as T;
  }

  if (typeof obj === 'object') {
    const trimmedObj = {} as T;
    for (const [key, value] of Object.entries(obj)) {
      (trimmedObj as any)[key] = deepTrimStrings(value);
    }
    return trimmedObj;
  }

  return obj;
}
