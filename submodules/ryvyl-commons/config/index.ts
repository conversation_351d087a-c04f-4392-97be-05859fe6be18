/**
 * Get the environment variable or revert with an error
 * @param name - The name of the environment variable
 * @returns The environment variable
 */
export const getEnvOrRevert = (name: string): string => {
  const env = process.env[name];
  if (!env || env === '') {
    throw new Error(`Couldn't find environment variable: ${name}`);
  }

  return env;
};

/**
 * Get the comma-separated environment variable as an array
 * @param name - The name of the environment variable
 * @param isRequired - Whether the environment variable is required, default is true
 * @returns The environment variable as an array
 */
export const getListEnvironmentVariableAsArray = (name: string, isRequired = true): string[] => {
  if (isRequired) {
    return getEnvOrRevert(name).split(',');
  }

  const env = process.env[name];
  if (!env || env === '') {
    return [];
  }

  return env.split(',');
};

export const IS_PRODUCTION = process.env['ENV'] === 'PROD';
export const IS_DEV = process.env['ENV'] === 'DEV';
export const PORT = process.env.PORT || 3000;
export const LOG_LEVEL = process.env.LOG_LEVEL || 'debug';

// JWT
export const JWT_PUBLIC_KEY = process.env.JWT_PUBLIC_KEY || '';
export const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY || '';
export const JWT_ALGORITHM = process.env.JWT_ALGORITHM || '';

export const KAFKA = {
  BROKERS: getListEnvironmentVariableAsArray('KAFKA_BROKER_URL',false),
  GROUP_ID: process.env.KAFKA_GROUP_ID || ''
};

export const REDIS = {
  URL: process.env.REDIS_URL || '',
  PASSWORD: process.env.REDIS_PASSWORD || '',
  PORT: process.env.REDIS_PORT || 6379
};
