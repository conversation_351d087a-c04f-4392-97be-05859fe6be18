import { NextFunction, Request, Response } from "express";
import { deepTrimStrings } from "../utils/trimBody";

/**
 * Middleware to trim the request body
 * @param req - The request object
 * @param res - The response object
 * @param next - The next function
 * @example
 * req.body = ["  string1  ", "  string2  ", { name: "  nested  " }, ['  array1  ', '  array2  ']]
 * Result: ["string1", "string2", { name: "nested" }, ['array1', 'array2']]
 */

export function trimRequestBodyMiddleware(req: Request, res: Response, next: NextFunction) {
  if (req.body !== null && req.body !== undefined) {
    req.body = deepTrimStrings(req.body);
  }
  next();
}
