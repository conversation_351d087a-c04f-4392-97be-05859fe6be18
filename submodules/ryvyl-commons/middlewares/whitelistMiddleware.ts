import { Request, Response, NextFunction } from 'express';

import { IPWhitelistLogger } from '../services/loggerService';
import { getClientIp } from '../utils/helper';

export const IP_WHITELIST = process.env.IP_WHITELIST?.split(',') || [];

// Extend the Request interface
declare global {
  namespace Express {
    interface Request {
      clientIp?: string;
    }
  }
}

/**
 * Creates a middleware that validates if the client's IP is in the allowed list
 * @param allowedIPs - Array of IP addresses that are allowed to access the endpoint
 * @returns Express middleware function
 */
export function ipWhitelistMiddleware(allowedIPs: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientIp = getClientIp(req);

    if (!clientIp || !allowedIPs.includes(clientIp)) {
      IPWhitelistLogger.error(`Access denied for IP: ${clientIp}`);
      res.status(403).json({ 
        error: 'Access denied',
        message: 'Your IP address is not authorized to access this resource'
      });
      return;
    }

    req.clientIp = clientIp;
    
    next();
  };
}

/**
 * Default IP whitelist middleware using the global IP_WHITELIST from environment variables
 */
export const verifyIP = ipWhitelistMiddleware(IP_WHITELIST);
