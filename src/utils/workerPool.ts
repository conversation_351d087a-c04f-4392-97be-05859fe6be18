import { Worker } from 'worker_threads';
import * as path from 'path';
import * as os from 'os';

interface WorkerTask {
  taskId: string;
  data: any;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}

interface WorkerInfo {
  worker: Worker;
  busy: boolean;
}

export class WorkerPool {
  private workers: WorkerInfo[] = [];
  private taskQueue: WorkerTask[] = [];
  private maxWorkers: number;
  private workerScript: string;

  constructor(workerScript: string, maxWorkers?: number) {
    this.workerScript = workerScript;
    this.maxWorkers = maxWorkers || Math.min(os.cpus().length, 4); // Use CPU cores but cap at 4
    this.initializeWorkers();
  }

  private initializeWorkers(): void {
    for (let i = 0; i < this.maxWorkers; i++) {
      this.createWorker();
    }
  }

  private createWorker(): void {
    const worker = new Worker(this.workerScript);
    const workerInfo: WorkerInfo = {
      worker,
      busy: false
    };

    worker.on('message', (message) => {
      workerInfo.busy = false;

      if (message.success) {
        // Find the task that corresponds to this result
        const taskIndex = this.taskQueue.findIndex((task) => !task.resolve); // Find completed task
        if (taskIndex !== -1) {
          const task = this.taskQueue.splice(taskIndex, 1)[0];
          task.resolve(message.result);
        }
      } else {
        const taskIndex = this.taskQueue.findIndex((task) => !task.reject);
        if (taskIndex !== -1) {
          const task = this.taskQueue.splice(taskIndex, 1)[0];
          task.reject(new Error(message.error));
        }
      }

      // Process next task in queue
      this.processNextTask();
    });

    worker.on('error', (error) => {
      workerInfo.busy = false;
      console.error('Worker error:', error);
      // Recreate the worker
      this.workers = this.workers.filter((w) => w !== workerInfo);
      this.createWorker();
    });

    this.workers.push(workerInfo);
  }

  public execute<T>(taskId: string, data: any): Promise<T> {
    return new Promise((resolve, reject) => {
      const task: WorkerTask = {
        taskId,
        data,
        resolve,
        reject
      };

      this.taskQueue.push(task);
      this.processNextTask();
    });
  }

  private processNextTask(): void {
    if (this.taskQueue.length === 0) return;

    const availableWorker = this.workers.find((w) => !w.busy);
    if (!availableWorker) return;

    const task = this.taskQueue.shift();
    if (!task) return;

    availableWorker.busy = true;
    availableWorker.worker.postMessage(task.data);
  }

  public async executeAll<T>(tasks: Array<{ taskId: string; data: any }>): Promise<T[]> {
    const promises = tasks.map((task) => this.execute<T>(task.taskId, task.data));
    return Promise.all(promises);
  }

  public terminate(): void {
    this.workers.forEach((workerInfo) => {
      workerInfo.worker.terminate();
    });
    this.workers = [];
    this.taskQueue = [];
  }
}

// Singleton instance for string comparison
let stringComparisonPool: WorkerPool | null = null;

export function getStringComparisonPool(): WorkerPool {
  if (!stringComparisonPool) {
    // Try different paths for the worker script
    let workerScript: string;

    // In development (TypeScript)
    const tsWorkerPath = path.join(__dirname, '../workers/stringComparisonWorker.ts');
    // In production (compiled JavaScript)
    const jsWorkerPath = path.join(__dirname, '../workers/stringComparisonWorker.js');
    // Alternative path for dist folder
    const distWorkerPath = path.join(process.cwd(), 'dist/workers/stringComparisonWorker.js');

    if (require('fs').existsSync(jsWorkerPath)) {
      workerScript = jsWorkerPath;
    } else if (require('fs').existsSync(distWorkerPath)) {
      workerScript = distWorkerPath;
    } else {
      // Fallback: try to use ts-node for TypeScript execution
      workerScript = tsWorkerPath;
    }

    stringComparisonPool = new WorkerPool(workerScript);
  }
  return stringComparisonPool;
}

export function terminateStringComparisonPool(): void {
  if (stringComparisonPool) {
    stringComparisonPool.terminate();
    stringComparisonPool = null;
  }
}
