import { Express } from 'express';
import dotenv from 'dotenv';
import http from 'http';

dotenv.config();

import { app } from './app';
import logger from '../submodules/ryvyl-commons/services/loggerService';

let server: http.Server | undefined;

const PORT = process.env.PORT || 46001;

app.on('ready', async () => {
  server = app.listen(PORT, () => logger.info(`Server is listening on port: ${PORT}`));
});

process.on('SIGTERM', () => {
  if (server) {
    server.close(() => {
      logger.info('The server has been stopped.');
    });
  }
});

// To disconnect the server when we press 'ctrl + c' on the terminal
process.on('SIGINT', () => {
  if (server) {
    server.close(() => {
      logger.info('The server has been stopped.');
      process.exit(0); // Exit the process after server is closed
    });
  }
});

/**
 * Function to be executed after successful connection
 *
 * @param app The Express app
 */
function afterConnect(app: Express): void {
  app.emit('ready');
}

afterConnect(app);
