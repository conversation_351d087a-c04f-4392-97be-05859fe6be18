export const getEnvOrRevert = (name: string): string => {
  const env = process.env[name];
  if (!env || env === '') {
    throw new Error(`Couldn't find environment variable: ${name}`);
  }
  return env;
};

export const isProduction = process.env['ENV'] === 'PROD';

export const DB = {
  HOST: process.env.MONGODB_HOST || 'localhost',
  PORT: process.env.MONGODB_PORT || '27017',
  NAME: process.env.MONGODB_DATABASE_NAME || '',
  USERNAME: process.env.MONGODB_USERNAME || '',
  PASSWORD: process.env.MONGODB_PASSWORD || '',
  URI: process.env.MONGODB_URI || '',
  CA_CERT: process.env.CA_CERT
};
