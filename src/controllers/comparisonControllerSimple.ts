import { Request, Response } from 'express';

export async function compareAllToAllSimple(req: Request, res: Response): Promise<any> {
  const { target, source } = req.body;

  console.log('Target: ', target.length);
  console.log('Source: ', source.length);

  try {
    console.log('Using simple chunked processing...');
    
    // Process in small chunks to avoid blocking
    const chunkSize = 10; // Process 10 items at a time
    const results: any[] = [];
    
    for (let i = 0; i < target.length; i += chunkSize) {
      const chunk = target.slice(i, i + chunkSize);
      
      console.log(`Processing chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(target.length / chunkSize)}`);
      
      const chunkResults = chunk.map((targetItem: any, chunkIndex: number) => {
        const globalIndex = i + chunkIndex;
        
        const scoredSourceItems = source.map((sourceItem: any) => {
          const pspAccuracy = getAccuracy(targetItem.pspName || '', sourceItem.psp || '');
          const merchantAccuracy = getAccuracy(targetItem.companyName || '', sourceItem.merchant || '');
          return {
            ...sourceItem,
            pspAccuracy,
            merchantAccuracy
          };
        });

        const filteredItems = scoredSourceItems.filter((sourceItem: any) => {
          return sourceItem.pspAccuracy >= 0.9 || sourceItem.merchantAccuracy >= 0.9;
        });

        const matchItem = filteredItems.length > 0
          ? filteredItems.sort(
              (a: any, b: any) => b.pspAccuracy - a.pspAccuracy || b.merchantAccuracy - a.merchantAccuracy
            )[0]
          : null;

        console.log(`Match No: ${globalIndex}, ${JSON.stringify(matchItem)}`);
        return matchItem;
      });
      
      results.push(...chunkResults);
      
      // Yield control to event loop between chunks
      await new Promise(resolve => setImmediate(resolve));
    }

    console.log('Processing completed successfully');
    res.status(200).json(results);
    
  } catch (error) {
    console.error('Error in simple comparison:', error);
    res.status(500).json({ error: 'Internal server error during comparison' });
  }
}

function jaro(s1: string, s2: string): number {
  var s1_len = s1.length;
  var s2_len = s2.length;
  if (s1_len === 0 && s2_len === 0) return 1.0;
  var match_distance = Math.floor(Math.max(s1_len, s2_len) / 2) - 1;
  var s1_matches = new Array(s1_len).fill(false);
  var s2_matches = new Array(s2_len).fill(false);
  var matches = 0;
  var transpositions = 0;
  for (var i = 0; i < s1_len; i++) {
    var start = Math.max(0, i - match_distance);
    var end = Math.min(i + match_distance + 1, s2_len);
    for (var j = start; j < end; j++) {
      if (s2_matches[j]) continue;
      if (s1[i] !== s2[j]) continue;
      s1_matches[i] = true;
      s2_matches[j] = true;
      matches++;
      break;
    }
  }
  if (matches === 0) return 0.0;
  var k = 0;
  for (var i = 0; i < s1_len; i++) {
    if (!s1_matches[i]) continue;
    while (!s2_matches[k]) k++;
    if (s1[i] !== s2[k]) transpositions++;
    k++;
  }
  return (matches / s1_len + matches / s2_len + (matches - transpositions / 2) / matches) / 3;
}

function getAccuracy(s1: string, s2: string): number {
  var jaro_sim = jaro(s1, s2);
  var prefix = 0;
  var max_prefix = 4;
  for (var i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) prefix++;
    else break;
  }
  prefix = Math.min(prefix, max_prefix);
  return jaro_sim + 0.1 * prefix * (1 - jaro_sim);
}
