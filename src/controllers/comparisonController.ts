import { Request, Response } from 'express';
import { getStringComparisonPool } from '../utils/workerPool';

export async function compareAllToAll(req: Request, res: Response): Promise<any> {
  const { target, source } = req.body;

  console.log('Target: ', target.length);
  console.log('Source: ', source.length);

  try {
    const workerPool = getStringComparisonPool();

    // Create tasks for parallel processing
    const tasks = (target as any[]).map((targetItem, index) => ({
      taskId: `comparison-${index}`,
      data: {
        targetItem,
        sourceItems: source,
        taskId: `comparison-${index}`
      }
    }));

    console.log(`Processing ${tasks.length} comparisons in parallel using worker threads...`);

    // Execute all comparisons in parallel using worker threads
    const results = await workerPool.executeAll(tasks);

    // Sort results by target index to maintain order
    const sortedResults = results.sort((a: any, b: any) => a.targetIndex - b.targetIndex);

    // Extract just the match items and log them
    const matchItems = sortedResults.map((result: any, index: number) => {
      console.log(`Match No: ${index}, ${JSON.stringify(result.matchItem)}`);
      return result.matchItem;
    });

    res.status(200).json(matchItems);
  } catch (error) {
    console.error('Error in parallel comparison:', error);
    res.status(500).json({ error: 'Internal server error during comparison' });
  }
}
