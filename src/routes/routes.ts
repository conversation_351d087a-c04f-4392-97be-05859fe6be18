import express from 'express';
import logger from '../../submodules/ryvyl-commons/services/loggerService';
import { compareAllToAll } from '../controllers/comparisonController';
import { compareAllToAllSimple } from '../controllers/comparisonControllerSimple';

const routes = express.Router();

// Health-check endpoint
routes.get('', function (_req: express.Request, res: express.Response) {
  logger.info(`Health-check probe/readiness: ${Date.now()}`);
  res.status(200).send('OK');
});

// Set no timeout for the comparison endpoint
routes.post(
  '/compare/all-to-all',
  (req, res, next) => {
    // Remove timeout for this specific endpoint
    req.setTimeout(0); // 0 means no timeout
    res.setTimeout(0); // 0 means no timeout
    next();
  },
  compareAllToAll
);

export default routes;
