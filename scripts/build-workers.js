const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Ensure the workers directory exists in dist
const distWorkersDir = path.join(__dirname, '../dist/workers');
if (!fs.existsSync(distWorkersDir)) {
  fs.mkdirSync(distWorkersDir, { recursive: true });
}

try {
  // Compile the worker TypeScript file
  console.log('Compiling worker TypeScript files...');
  execSync('npx tsc src/workers/stringComparisonWorker.ts --outDir dist/workers --target es2020 --module commonjs --esModuleInterop --strict', {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('Worker compilation completed successfully!');
} catch (error) {
  console.error('Error compiling workers:', error.message);
  process.exit(1);
}
